﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class OperationClaimManager : IOperationClaimService
    {
        IOperationClaimDal _operationClaimDal;

        public OperationClaimManager(IOperationClaimDal operationClaimDal)
        {
            _operationClaimDal = operationClaimDal;
        }
        [SecuredOperation("owner")]
        [CacheRemoveAspect("gym:*:operationclaim:*")]
        public IResult Add(OperationClaim operationClaim)
        {
            _operationClaimDal.Add(operationClaim);
            return new SuccessResult(Messages.OperationClaimAdded);
        }
        [SecuredOperation("owner")]
        [CacheRemoveAspect("gym:*:operationclaim:*")]
        public IResult Delete(int id)
        {
            _operationClaimDal.HardDelete(id);
            return new SuccessResult(Messages.OperationClaimDeleted);
        }
        [SecuredOperation("owner")]
        [CacheAspect(86400)] // 24 saat cache - System roles (master data)
        public IDataResult<List<OperationClaim>> GetAll()
        {
            return new SuccessDataResult<List<OperationClaim>>(_operationClaimDal.GetAll(), Messages.OperationClaimsListed);
        }
        [SecuredOperation("owner")]
        [CacheAspect(86400)] // 24 saat cache - Rol detayı
        public IDataResult<OperationClaim> GetById(int id)
        {
            return new SuccessDataResult<OperationClaim>(_operationClaimDal.Get(o => o.OperationClaimId == id));
        }
        [SecuredOperation("owner")]
        [CacheRemoveAspect("gym:*:operationclaim:*")]
        public IResult Update(OperationClaim operationClaim)
        {
            _operationClaimDal.Update(operationClaim);
            return new SuccessResult(Messages.OperationClaimUpdated);
        }

        [LogAspect] // Loglama eklendi
        [CacheAspect(86400)] // 24 saat cache - Rol adına göre arama
        public IDataResult<OperationClaim> GetByName(string name)
        {
            // SOLID prensiplerine uygun: Karmaşık validation ve logging işlemini DAL katmanına taşıdık
            return _operationClaimDal.GetOperationClaimByNameWithValidation(name);
        }
    }
}